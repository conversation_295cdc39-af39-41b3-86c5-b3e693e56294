package com.thedasagroup.suminative.ui.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.github.kittinunf.fuel.Fuel
import com.github.kittinunf.fuel.core.extensions.jsonBody
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.App
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.api.SOCKET
import com.thedasagroup.suminative.data.model.request.notification.NotificationRequest
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.ui.MainActivity
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase
import com.thedasagroup.suminative.ui.utils.ChatWebSocketClient
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
import com.thedasagroup.suminative.ui.utils.getDateFromHourAndMinute
import com.thedasagroup.suminative.ui.utils.getDayOfWeek
import com.thedasagroup.suminative.ui.utils.toGMT
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import java.net.URI
import java.text.SimpleDateFormat
import java.util.Date
import javax.inject.Inject

@AndroidEntryPoint
class EndlessSocketService : Service() {

    private val TAG: String? = EndlessSocketService::class.simpleName

    @Inject
    lateinit var prefs: Prefs

    @Inject
    lateinit var orderRepository: OrdersRepository

    @Inject
    lateinit var getStoreSettings: GetStoreSettingsUseCase

    @Inject
    lateinit var trueTimeImpl: TrueTimeImpl

    @Inject
    lateinit var soundPoolPlayer: SoundPoolPlayer

    private var wakeLock: PowerManager.WakeLock? = null
    private var isServiceStarted = false

    private var webSocketClient2: ChatWebSocketClient? = null

    companion object {
        var dialogType: String = ""
        var dialogMessage: String = ""
        var flowIsConnected2: MutableStateFlow<Boolean> = MutableStateFlow(false)
        var lostConnectionDate: MutableStateFlow<Date> = MutableStateFlow(Date())
    }

    override fun onBind(intent: Intent): IBinder? {
        log("Some component want to bind with the service")
        // We don't provide binding, so return null
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        log("onStartCommand executed with startId: $startId")
        if (intent != null) {
            val action = intent.action
            log("using an intent with action $action")
            when (action) {
                Actions.START.name -> startService()
                Actions.STOP.name -> stopService()
                else -> log("This should never happen. No action in the received intent")
            }
        } else {
            log(
                "with a null intent. It has been probably restarted by the system."
            )
        }
        // by returning this we make sure the service is restarted if the system kills the service
        return START_STICKY
    }

    override fun onCreate() {
        super.onCreate()
        log("The service has been created".uppercase())
        val notification = createNotification()
        startForeground(1, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        log("The service has been destroyed".uppercase())
//        Toast.makeText(this, "Service destroyed", Toast.LENGTH_SHORT).show()
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun startService() {
        if (isServiceStarted) return
        log("Starting the foreground service task")
//        Toast.makeText(this, "Service starting its task", Toast.LENGTH_SHORT).show()
        isServiceStarted = true
        setServiceState(this, ServiceState.STARTED)

        // we need this lock so our service gets not affected by Doze Mode
        wakeLock = (getSystemService(Context.POWER_SERVICE) as PowerManager).run {
            newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "EndlessService::lock").apply {
                acquire()
            }
        }

        // we're starting a loop in a coroutine
        GlobalScope.launch(Dispatchers.Main) {
            withContext(Dispatchers.IO) {
                while (isServiceStarted) {
                    if (webSocketClient2?.isOpen != true) {
                        val serverUri2 = URI(SOCKET)
                        webSocketClient2 = ChatWebSocketClient(serverUri2) { message ->
                            GlobalScope.launch(Dispatchers.Main) {
                                runCatching {
                                    run {
                                        // Handle table update messages with action "update"
                                        if (message.contains("\"action\":")) {
                                            try {
                                                val tableUpdateModel = Json.decodeFromString<TableUpdateSocket>(message)
                                                Log.v(TAG, "Table update message: $tableUpdateModel")

                                                // Send silent notification for table update
                                                sendMessageOrNotification(
                                                    message = message,
                                                    type = "table_update",
                                                    isNotSilent = false,
                                                    loops = 0
                                                )
                                            } catch (e: Exception) {
                                                Log.e(TAG, "Error parsing table update message: ${e.message}")
                                            }
                                        } else if (message.lowercase().contains("store settings")) {
                                            sendMessageOrNotification(
                                                message = message,
                                                type = "update_store_timings",
                                                isNotSilent = false,
                                                loops = 10
                                            )
                                            sendSocketNotification(message, orderId = 0)
                                        } else {
                                            val parts = message.split(":")
                                            Log.v(TAG, "message: $parts")
                                            val isScheduleOrder = parts.firstOrNull {
                                                it.contains("true")
                                            } != null
                                            val isPOSOrder = message.contains("POS", ignoreCase = true)
                                            val orderId = parts.last().split("order id ").last()
                                            if (parts[2] == prefs.store?.id.toString() && !isPOSOrder) {
                                                sendMessageOrNotification(
                                                    message = parts[0],
                                                    type = "new_order",
                                                    isNotSilent = true,
                                                    sound = R.raw.notification,
                                                    loops = 60,
                                                    isScheduleOrder = isScheduleOrder
                                                )
                                                sendSocketNotification(
                                                    message, orderId = orderId.toInt()
                                                )
                                            }
                                        }
                                    if (message.lowercase()
                                            .contains("manually opened") || message.lowercase()
                                            .contains("manually closed")
                                    ) {
                                        sendMessageOrNotification(
                                            message = message,
                                            type = "store_open_close",
                                            isNotSilent = false,
                                            loops = 10
                                        )
                                    } else {
                                        val socketModel =
                                            Json.decodeFromString<UpdateOrderSocket>(message)
                                        Log.v(TAG, "message: ${socketModel}")
                                        val parts = socketModel.message?.split(":")
                                        if (socketModel.type == "Store") {
                                            val orderId = 0
                                            if ((parts?.get(1)
                                                    ?: "") == prefs.store?.id.toString()
                                            ) {
                                                if (parts?.get(0)?.contains("canceled") == true) {
                                                    sendMessageOrNotification(
                                                        message = parts[0],
                                                        type = "cancel_order",
                                                        isNotSilent = true,
                                                        sound = R.raw.cancelled_order,
                                                        loops = 10
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                } else if (parts?.get(0)
                                                        ?.contains("assigned") == true && parts[0].lowercase()
                                                        .contains("courier")
                                                ) {
                                                    sendMessageOrNotification(
                                                        message = parts[0],
                                                        type = "update_order_with_dialog",
                                                        isNotSilent = true,
                                                        sound = R.raw.courier_assigned,
                                                        loops = 10
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                } else if (parts?.get(0)?.lowercase()
                                                        ?.contains("courier") == true && parts[0].lowercase()
                                                        .contains("arriving") && parts[0].lowercase()
                                                        .contains("pickup")
                                                ) {
                                                    sendMessageOrNotification(
                                                        message = parts[0],
                                                        type = "update_order_with_dialog",
                                                        isNotSilent = true,
                                                        sound = R.raw.courier_arriving,
                                                        loops = 10
                                                    )
                                                    sendSocketNotification(message, orderId = 0)
                                                } else if (parts?.get(0)?.lowercase()
                                                        ?.contains("courier") == true && parts[0].lowercase()
                                                        .contains("arriving") && parts[0].lowercase()
                                                        .contains("dropoff")
                                                ) {
                                                    sendMessageOrNotification(
                                                        message = "Drop Off Imminent",
                                                        type = "update_order_with_dialog",
                                                        isNotSilent = true,
                                                        sound = R.raw.delivery_imminent,
                                                        loops = 10
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                } else if (parts?.get(0)?.lowercase()
                                                        ?.contains("out for delivery") == true
                                                ) {
                                                    sendMessageOrNotification(
                                                        message = parts[0],
                                                        type = "update_order_with_dialog",
                                                        isNotSilent = true,
                                                        sound = R.raw.out_for_delivery,
                                                        loops = 10
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                } else if (parts?.get(0)?.lowercase()
                                                        ?.contains("delivered") == true
                                                ) {
                                                    sendMessageOrNotification(
                                                        message = parts[0],
                                                        type = "update_order_with_dialog",
                                                        isNotSilent = true,
                                                        sound = R.raw.delivered,
                                                        loops = 2
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                } else if (parts?.get(0)?.lowercase()
                                                        ?.contains("is due in") == true
                                                ) {
                                                    sendMessageOrNotification(
                                                        message = parts[0],
                                                        type = "update_order_with_dialog",
                                                        isNotSilent = true,
                                                        sound = R.raw.schedule_order,
                                                        loops = 2
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                } else {
                                                    sendMessageOrNotification(
                                                        message = parts?.get(0) ?: "",
                                                        type = "unknown_status",
                                                        isNotSilent = false,
                                                        sound = R.raw.notification,
                                                        loops = 10
                                                    )
                                                    sendSocketNotification(
                                                        message, orderId = orderId
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // connect to websocket server
                    webSocketClient2?.connect()
                }
//                launch(Dispatchers.IO) {
//                    pingFakeServer()
//                }
                delay(1 * 10 * 1000)
                checkStoreTimings()
                flowIsConnected2.value = webSocketClient2?.isOpen ?: false

//                sendMessageOrNotification(
//                    "updateOrdersLoop", "update_orders_loop", isNotSilent = false, loops = 0
//                )
            }
        }
        log("End of the loop for the service")
        schedule()
    }

}

private fun stopService() {
    log("Stopping the foreground service")
//        Toast.makeText(this, "Service stopping", Toast.LENGTH_SHORT).show()
    try {
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
        webSocketClient2?.close()
        stopForeground(true)
        val mgr = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        mgr.cancel(1234)
        stopSelf()
    } catch (e: Exception) {
        log("Service stopped without being started: ${e.message}")
    }
    isServiceStarted = false
    setServiceState(this, ServiceState.STOPPED)
}

private fun pingFakeServer() {
    val df = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.mmmZ")
    val gmtTime = df.format(Date())

    val deviceId = Settings.Secure.getString(
        applicationContext.contentResolver, Settings.Secure.ANDROID_ID
    )

    val json = """
                {
                    "deviceId": "$deviceId",
                    "createdAt": "$gmtTime"
                }
            """
    try {
        Fuel.post("https://jsonplaceholder.typicode.com/posts").jsonBody(json)
            .response { _, _, result ->
                val (bytes, error) = result
                if (bytes != null) {
                    log("[response bytes] ${String(bytes)}")
                } else {
                    schedule()
                    stopService()
                    log("[response error] ${error?.message}")
                }
            }
    } catch (e: Exception) {
        log("Error making the request: ${e.message}")
        schedule()
        stopService()
    }
}

fun schedule() {
//        if(webSocketClient?.isOpen == false && webSocketClient2?.isOpen == false){
//            playSound(R.raw.delivered, loops = 1)
//        }
    val constraints = Constraints.Builder()
        .setRequiredNetworkType(NetworkType.CONNECTED) // This line sets the network type constraint
        .build()

    val networkChangeWorkRequest =
        OneTimeWorkRequestBuilder<NetworkChangeWorker>().setConstraints(constraints).build()

    WorkManager.getInstance(applicationContext).enqueue(networkChangeWorkRequest)
}

private fun createNotification(): Notification {
    val notificationChannelId = "ENDLESS SERVICE CHANNEL"

    // depending on the Android API that we're dealing with we will have
    // to use a specific method to create the notification
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager;
        val channel = NotificationChannel(
            notificationChannelId,
            "Endless Service notifications channel",
            NotificationManager.IMPORTANCE_HIGH
        ).let {
            it.description = "Endless Service channel"
            it.enableLights(true)
            it.lightColor = Color.RED
            it.enableVibration(true)
            it.vibrationPattern = longArrayOf(100, 200, 300, 400, 500, 400, 300, 200, 400)
            it
        }
        notificationManager.createNotificationChannel(channel)
    }

    val pendingIntent: PendingIntent =
        Intent(this@EndlessSocketService, MainActivity::class.java).let { notificationIntent ->
            PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)
        }

    val builder: Notification.Builder =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) Notification.Builder(
            this, notificationChannelId
        ) else Notification.Builder(this)

    return builder.setContentTitle("DasaDirect").setContentText("Looking for Orders")
        .setContentIntent(pendingIntent).setSmallIcon(R.mipmap.ic_launcher).setTicker("Ticker text")
        .setOngoing(true)
        .setPriority(Notification.PRIORITY_HIGH) // for under android 26 compatibility
        .build()
}


private fun sendNotificationForOrder(message: String) {
    val notificationChannelId = "Order Notification Channel"

    // depending on the Android API that we're dealing with we will have
    // to use a specific method to create the notification
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        val notificationManager =
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager;
        val channel = NotificationChannel(
            notificationChannelId,
            "Endless Service notifications channel",
            NotificationManager.IMPORTANCE_HIGH
        ).let {
            it.description = "Endless Service channel"
            it.enableLights(true)
            it.lightColor = Color.RED
            it.enableVibration(true)
            it.vibrationPattern = longArrayOf(100, 200, 300, 400, 500, 400, 300, 200, 400)
            it
        }
        notificationManager.createNotificationChannel(channel)
    }

    val pendingIntent: PendingIntent =
        Intent(this@EndlessSocketService, MainActivity::class.java).let { notificationIntent ->
            PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)
        }

    val builder: Notification.Builder =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) Notification.Builder(
            this, notificationChannelId
        ) else Notification.Builder(this)

    val notification = builder.setContentTitle("DasaDirect").setContentText(message)
        .setContentIntent(pendingIntent).setSmallIcon(R.mipmap.ic_launcher).setOngoing(false)
        .setSound(Settings.System.DEFAULT_NOTIFICATION_URI)
        .setPriority(Notification.PRIORITY_HIGH) // for under android 26 compatibility
        .build()

    val notificaionManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    notificaionManager.notify(1111, notification)

}

private fun sendSocketNotification(message: String, orderId: Int) {
    GlobalScope.launch(Dispatchers.IO) {
        val androidId = Settings.Secure.getString(
            applicationContext.contentResolver, Settings.Secure.ANDROID_ID
        )
        Log.d(TAG, "sendSocketNotification: ${androidId}")
        orderRepository.addNotification(
            request = NotificationRequest(
                deviceId = androidId,
                orderId = orderId,
                type = message,
                storeId = prefs.store?.id ?: 0
            )
        ).collectLatest {

        }
    }
}

private fun sendMessageOrNotification(
    message: String,
    type: String,
    isNotSilent: Boolean = true,
    sound: Int = R.raw.notification,
    loops: Int,
    isScheduleOrder: Boolean = false
) {
    val app = applicationContext as App
    Log.d(TAG, "Broadcasting message")
    val intent = Intent("custom-event-name")
    // You can also include some extra data.
    intent.putExtra("message", message)
    intent.putExtra("type", type)
    intent.putExtra("isScheduleOrder", isScheduleOrder)
    LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
//    if (app.isActivityVisible) {
//    } else {
//        if (isNotSilent) {
//            sendNotificationForOrder(message)
//        }
//    }
//    if (isNotSilent) {
//        soundOnOrder(message = message, type = type, sound = sound, loops = loops)
//    }
}

fun soundOnOrder(message: String, type: String, sound: Int, loops: Int) {
    dialogMessage = message
    dialogType = type
    when (type) {
        "new_order" -> {
            playSound(sound, loops = 60)
        }

        "update_order" -> {
            playSound(sound, loops = loops)
        }

        "update_order_with_dialog" -> {
            playSound(sound, loops = loops)
        }

        "cancel_order" -> {
            playSound(sound, loops = loops)
        }
    }
}

fun playSound(sound: Int, loops: Int = 10) {
    soundPoolPlayer.stop(SoundPoolPlayer.streamId)
    soundPoolPlayer.resId = sound
    soundPoolPlayer.context = this
    soundPoolPlayer.loops = loops
    soundPoolPlayer.play()
}

fun checkStoreTimings() {
    val nowDate = trueTimeImpl.now().toGMT()
    val weekday: String = nowDate.getDayOfWeek()
    val storeSettings = prefs.storeSettings
    val timing = storeSettings?.timingJson?.firstOrNull {
        it.day == weekday
    }
    val openHour = timing?.openingTime?.split(":")?.get(0)?.toInt() ?: 0
    val openMinute = timing?.openingTime?.split(":")?.get(1)?.toInt() ?: 0
    val closeHour = timing?.closingTime?.split(":")?.get(0)?.toInt() ?: 0
    val closeMinute = timing?.closingTime?.split(":")?.get(1)?.toInt() ?: 0
    val openDate = getDateFromHourAndMinute(openHour, openMinute, nowDate = nowDate).toGMT()
    val closeDate = getDateFromHourAndMinute(closeHour, closeMinute, nowDate = nowDate).toGMT()
    if (nowDate.after(openDate) && nowDate.before(closeDate)) { //store open
        if (prefs.storeClosed) {
            sendMessageOrNotification(
                message = "Store Opened",
                type = "update_store_timings",
                isNotSilent = false,
                loops = 10
            )
        }
    } else { // store closed
        if (!prefs.storeClosed) {
            sendMessageOrNotification(
                message = "Store Closed",
                type = "update_store_timings",
                isNotSilent = false,
                loops = 10
            )
        }
    }
}

}

fun log(message: String) {
    Log.v("EndlessService", message)
}
